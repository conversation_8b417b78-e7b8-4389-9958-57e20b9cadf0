{"version": 4, "routes": {"/balicky/zakladna": {"initialRevalidateSeconds": false, "srcRoute": "/balicky/[slug]", "dataRoute": "/_next/data/nh2MPKsZ9Mbk0i3TLp22O/balicky/zakladna.json"}, "/balicky/premium": {"initialRevalidateSeconds": false, "srcRoute": "/balicky/[slug]", "dataRoute": "/_next/data/nh2MPKsZ9Mbk0i3TLp22O/balicky/premium.json"}, "/balicky/exclusive": {"initialRevalidateSeconds": false, "srcRoute": "/balicky/[slug]", "dataRoute": "/_next/data/nh2MPKsZ9Mbk0i3TLp22O/balicky/exclusive.json"}}, "dynamicRoutes": {"/balicky/[slug]": {"routeRegex": "^/balicky/([^/]+?)(?:/)?$", "dataRoute": "/_next/data/nh2MPKsZ9Mbk0i3TLp22O/balicky/[slug].json", "fallback": false, "dataRouteRegex": "^/_next/data/nh2MPKsZ9Mbk0i3TLp22O/balicky/([^/]+?)\\.json$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "0b7862cf3f76356e91fd3c295c637729", "previewModeSigningKey": "8f3ada5e8e99bc6b1915c078e093546bc7bd12aa9f05caf88ec1cf164e6ec727", "previewModeEncryptionKey": "e00a11145652aac5cbe901866813dce76bb716a9a4d25f628c75ca5d95b80556"}}