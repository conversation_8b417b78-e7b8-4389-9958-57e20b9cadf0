{"version": 4, "routes": {"/balicky/zakladna": {"initialRevalidateSeconds": false, "srcRoute": "/balicky/[slug]", "dataRoute": "/_next/data/CObUNaoXH6VTpC8z-KqQs/balicky/zakladna.json"}, "/balicky/premium": {"initialRevalidateSeconds": false, "srcRoute": "/balicky/[slug]", "dataRoute": "/_next/data/CObUNaoXH6VTpC8z-KqQs/balicky/premium.json"}, "/balicky/exclusive": {"initialRevalidateSeconds": false, "srcRoute": "/balicky/[slug]", "dataRoute": "/_next/data/CObUNaoXH6VTpC8z-KqQs/balicky/exclusive.json"}}, "dynamicRoutes": {"/balicky/[slug]": {"routeRegex": "^/balicky/([^/]+?)(?:/)?$", "dataRoute": "/_next/data/CObUNaoXH6VTpC8z-KqQs/balicky/[slug].json", "fallback": false, "dataRouteRegex": "^/_next/data/CObUNaoXH6VTpC8z\\-KqQs/balicky/([^/]+?)\\.json$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "d95d36deb1fdda9b34ac80400180cfd7", "previewModeSigningKey": "3ff833f985fd17e3d7171710b8f49a5857548e04627f899b6fba64e34963ae17", "previewModeEncryptionKey": "789586e30358bf061fb55886afab46a4260a06f82b4285b5e757f392a601fdc6"}}