import { GetStaticPaths, GetStaticProps } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import LoadingSpinner from '../../components/LoadingSpinner'
import Breadcrumb from '../../components/Breadcrumb'

interface PackageData {
  slug: string
  name: string
  price: string
  emoji: string
  shortDescription: string
  description: string
  features: string[]
  process: Array<{
    step: number
    title: string
    description: string
  }>
  idealFor: string[]
  comparison: {
    basic: boolean
    premium: boolean
    exclusive: boolean
  }
  faq: Array<{
    question: string
    answer: string
  }>
}

const packagesData: Record<string, PackageData> = {
  zakladna: {
    slug: 'zakladna',
    name: 'SPOMIENKA Základná',
    price: '299€',
    emoji: '🌸',
    shortDescription: 'Dôstojná online spomienka s elegantným riešením',
    description: 'Základn<PERSON> bal<PERSON> pre rodiny, ktoré chcú vytvoriť krásnu digitálnu spomienku na svojho milovaného. Obsahuje všetko potrebné pre dôstojnú online prezentáciu.',
    features: [
      'Memorial webstránka',
      'Až 15 fotografií',
      'Životopis',
      'Guestbook pre kondolencie',
      'Základný elegantný dizajn',
      'QR kód nálepka',
      'SSL certifikát',
      'Mobilná optimalizácia'
    ],
    process: [
      { step: 1, title: 'Konzultácia', description: 'Bezplatná konzultácia o vašich potrebách' },
      { step: 2, title: 'Materiály', description: 'Zhromaždenie fotografií a informácií' },
      { step: 3, title: 'Tvorba', description: 'Vytvorenie memorial webstránky' },
      { step: 4, title: 'Schválenie', description: 'Prezentácia a úpravy podľa želaní' },
      { step: 5, title: 'Spustenie', description: 'Aktivácia webstránky a QR kódu' },
      { step: 6, title: 'Platba', description: 'Platba až po úplnej spokojnosti' }
    ],
    idealFor: [
      'Rodiny hľadajúce jednoduché elegantné riešenie',
      'Základnú online prezentáciu spomienok',
      'Dostupné riešenie s profesionálnym vzhľadom'
    ],
    comparison: {
      basic: true,
      premium: false,
      exclusive: false
    },
    faq: [
      {
        question: 'Ako dlho trvá vytvorenie webstránky?',
        answer: 'Základná memorial webstránka je hotová do 5-7 pracovných dní od poskytnutia všetkých materiálov.'
      },
      {
        question: 'Môžem pridať viac fotografií neskôr?',
        answer: 'Áno, môžete dokúpiť rozšírenie na viac fotografií za príplatok 5€ za každú dodatočnú fotografiu.'
      },
      {
        question: 'Ako funguje QR kód?',
        answer: 'QR kód dostanete ako nálepku, ktorú môžete umiestniť na hrob. Po naskenovaní návštevníkov presmeruje na memorial webstránku.'
      }
    ]
  },
  premium: {
    slug: 'premium',
    name: 'SPOMIENKA Premium',
    price: '549€',
    emoji: '🏆',
    shortDescription: 'Kompletné riešenie s video spomienkou a rozšírenými funkciami',
    description: 'Najpopulárnejší balíček, ktorý kombinuje všetky funkcie základného balíčka s profesionálnym video obsahom a rozšírenými možnosťami.',
    features: [
      'Všetko zo Základnej',
      '2-minútové video spomienky',
      'Neobmedzené množstvo fotografií',
      'Profesionálny dizajn',
      'Časová os života',
      'Hudobné pozadie',
      'Kovová QR tabuľka',
      'Password ochrana',
      'Custom doména',
      'AI vylepšenie fotografií',
      'Personalizovaný text'
    ],
    process: [
      { step: 1, title: 'VIP Konzultácia', description: 'Rozšírená konzultácia s video plánom' },
      { step: 2, title: 'Materiály', description: 'Zhromaždenie fotografií, videí a hudby' },
      { step: 3, title: 'Video produkcia', description: 'Vytvorenie profesionálneho video obsahu' },
      { step: 4, title: 'Webstránka', description: 'Tvorba premium memorial webstránky' },
      { step: 5, title: 'Schválenie', description: 'Prezentácia a neobmedzené úpravy' },
      { step: 6, title: 'Dodanie', description: 'Spustenie a dodanie kovovej QR tabuľky' },
      { step: 7, title: 'Platba', description: 'Platba až po úplnej spokojnosti' }
    ],
    idealFor: [
      'Rodiny chcúce kompletné riešenie s videom',
      'Tých, ktorí chcú zachovať viac spomienok',
      'Profesionálnu prezentáciu s rozšírenými funkciami'
    ],
    comparison: {
      basic: true,
      premium: true,
      exclusive: false
    },
    faq: [
      {
        question: 'Aký typ videa vytvoríte?',
        answer: 'Vytvárame emotívne video spomienky s fotografiami, hudbou a textami. Video je optimalizované pre web aj mobilné zariadenia.'
      },
      {
        question: 'Môžem si vybrať hudbu?',
        answer: 'Áno, môžete si vybrať z našej knižnice alebo poskytnúť vlastnú hudbu. Pomôžeme vám vybrať najvhodnejšiu.'
      },
      {
        question: 'Čo obsahuje kovová QR tabuľka?',
        answer: 'Kovová tabuľka je odolná voči poveternostným vplyvom, obsahuje QR kód a môže mať gravírovaný text podľa vášho želania.'
      }
    ]
  },
  exclusive: {
    slug: 'exclusive',
    name: 'SPOMIENKA Exclusive',
    price: '899€',
    emoji: '💎',
    shortDescription: 'Luxusné riešenie s cinematic videom a VIP servisom',
    description: 'Najexkluzívnejší balíček s cinematic video produkciou, kompletným životopisom a white-glove servisom od začiatku do konca.',
    features: [
      'Všetko z Premium balíčka',
      '5-minútové cinematic video',
      'Kompletný životopis',
      'Personalizovaný dizajn',
      'Multimedia galéria',
      'Granitová QR tabuľka',
      'Profesionálne umiestnenie',
      'VIP konzultácie',
      'Professional production',
      'Installation service',
      'Rozšírená garancia'
    ],
    process: [
      { step: 1, title: 'VIP Konzultácia', description: 'Osobná konzultácia s kreativným tímom' },
      { step: 2, title: 'Kreativny plán', description: 'Vytvorenie detailného plánu projektu' },
      { step: 3, title: 'Produkcia', description: 'Cinematic video a kompletný obsah' },
      { step: 4, title: 'Dizajn', description: 'Personalizovaný dizajn webstránky' },
      { step: 5, title: 'Schválenie', description: 'Prezentácia a neobmedzené úpravy' },
      { step: 6, title: 'Inštalácia', description: 'Profesionálne umiestnenie QR tabuľky' },
      { step: 7, title: 'Platba', description: 'Platba až po úplnej spokojnosti' }
    ],
    idealFor: [
      'Rodiny chcúce najkvalitnejšie riešenie',
      'Kompletný white-glove servis',
      'Cinematic kvalitu a personalizáciu'
    ],
    comparison: {
      basic: true,
      premium: true,
      exclusive: true
    },
    faq: [
      {
        question: 'Čo znamená cinematic video?',
        answer: 'Cinematic video je profesionálne spracované s pokročilými efektmi, prechodmi a kvalitou ako vo filme. Trvá až 5 minút.'
      },
      {
        question: 'Čo obsahuje installation service?',
        answer: 'Náš tím profesionálne nainštaluje granitovú QR tabuľku na hrob a zabezpečí jej správne umiestnenie a funkčnosť.'
      },
      {
        question: 'Aká je rozšírená garancia?',
        answer: 'Poskytujeme 10-ročnú garanciu na všetky služby a bezplatné úpravy počas prvého roka.'
      }
    ]
  }
}

interface PackagePageProps {
  packageData: PackageData
}

export default function PackagePage({ packageData }: PackagePageProps) {
  const router = useRouter()

  useEffect(() => {
    if (packageData && typeof window !== 'undefined') {
      // Dynamic import to avoid SSR issues
      import('../../utils/analytics').then(({ trackPageView }) => {
        trackPageView(`${packageData.name} - Detail`)
      })
    }
  }, [packageData])

  const handleCTAClick = (ctaType: 'phone' | 'email', location: string) => {
    if (typeof window !== 'undefined') {
      import('../../utils/analytics').then(({ trackCTAClick }) => {
        trackCTAClick(ctaType, location)
      })
    }
  }

  if (router.isFallback) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Načítavam balíček...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>{packageData.name} - eSpomienka</title>
        <meta name="description" content={`${packageData.name} - ${packageData.description}`} />
        <meta name="keywords" content={`memorial, spomienky, ${packageData.slug}, video spomienky, QR kód, hrob`} />
        <meta property="og:title" content={`${packageData.name} - eSpomienka`} />
        <meta property="og:description" content={packageData.description} />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={`${packageData.name} - eSpomienka`} />
        <meta name="twitter:description" content={packageData.description} />
        <link rel="canonical" href={`https://espomienka.sk/balicky/${packageData.slug}`} />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Service",
              "name": packageData.name,
              "description": packageData.description,
              "provider": {
                "@type": "Organization",
                "name": "eSpomienka",
                "telephone": "+421951553464",
                "email": "<EMAIL>"
              },
              "offers": {
                "@type": "Offer",
                "price": packageData.price.replace('€', ''),
                "priceCurrency": "EUR"
              }
            })
          }}
        />
      </Head>

      <div className="min-h-screen">
        {/* Header */}
        <header className="bg-primary shadow-sm">
          <div className="container mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Link href="/" className="font-playfair text-2xl font-semibold text-white">
                  eSpomienka
                </Link>
              </div>
              <nav className="flex items-center space-x-8">
                <Link href="/" className="text-white hover:text-gold transition-colors">
                  Domov
                </Link>
                <Link href="/blog" className="text-white hover:text-gold transition-colors">
                  Blog
                </Link>
                <a href="tel:+421951553464" className="text-white hover:text-gold transition-colors">
                  +421 951 553 464
                </a>
              </nav>
            </div>
          </div>
        </header>

        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Domov', href: '/' },
            { label: 'Balíčky', href: '/#packages' },
            { label: packageData.name }
          ]}
        />

        {/* Package Hero */}
        <section className="py-16 bg-gradient-to-r from-primary to-primary-dark">
          <div className="container mx-auto px-6 text-center">
            <div className="text-6xl mb-4">{packageData.emoji}</div>
            <h1 className="text-4xl md:text-5xl font-playfair font-bold text-white mb-4">
              {packageData.name}
            </h1>
            <div className="text-5xl font-bold text-gold mb-6">{packageData.price}</div>
            <p className="text-xl text-gray-200 max-w-2xl mx-auto mb-8">
              {packageData.description}
            </p>
            <a
              href="tel:+421951553464"
              className="bg-gold hover:bg-gold-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-block"
              onClick={() => handleCTAClick('phone', 'hero')}
            >
              Objednať konzultáciu
            </a>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-playfair font-bold text-center text-gray-800 mb-12">
              Čo dostanete
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {packageData.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-gold rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                    </svg>
                  </div>
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section className="py-16 bg-cream">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-playfair font-bold text-center text-gray-800 mb-12">
              Ako to funguje
            </h2>
            <div className="max-w-4xl mx-auto">
              {packageData.process.map((step, index) => (
                <div key={index} className="flex items-start space-x-4 mb-8 last:mb-0">
                  <div className="w-12 h-12 bg-gold rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">{step.step}</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">{step.title}</h3>
                    <p className="text-gray-600">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Showcase Section - Placeholder */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-playfair font-bold text-center text-gray-800 mb-12">
              Ukážky našej práce
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3].map((item) => (
                <div key={item} className="bg-gray-100 rounded-lg p-8 text-center">
                  <div className="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-4"></div>
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">Pripravujeme ukážky</h3>
                  <p className="text-gray-500">Segera hadir</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Comparison Section */}
        <section className="py-16 bg-cream">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-playfair font-bold text-center text-gray-800 mb-12">
              Porovnanie balíčkov
            </h2>
            <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="grid grid-cols-4 gap-4 p-6 bg-gray-50 font-semibold">
                <div>Funkcie</div>
                <div className="text-center">Základná</div>
                <div className="text-center">Premium</div>
                <div className="text-center">Exclusive</div>
              </div>
              <div className="divide-y">
                {[
                  'Memorial webstránka',
                  'QR kód',
                  'Video spomienky',
                  'Neobmedzené fotky',
                  'Kovová/Granitová tabuľka',
                  'VIP servis'
                ].map((feature, index) => (
                  <div key={index} className="grid grid-cols-4 gap-4 p-4">
                    <div className="text-gray-700">{feature}</div>
                    <div className="text-center">
                      {index < 2 ? '✅' : '❌'}
                    </div>
                    <div className="text-center">
                      {index < 5 ? '✅' : '❌'}
                    </div>
                    <div className="text-center">✅</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-3xl font-playfair font-bold text-white mb-6">
              Pripravení začať?
            </h2>
            <p className="text-xl text-gray-200 max-w-2xl mx-auto mb-8">
              Kontaktujte nás pre bezplatnú konzultáciu. Platíte až keď ste úplne spokojní.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:+421951553464"
                className="bg-gold hover:bg-gold-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors"
                onClick={() => handleCTAClick('phone', 'cta_section')}
              >
                Zavolať +421 951 553 464
              </a>
              <a
                href="mailto:<EMAIL>"
                className="bg-white hover:bg-gray-100 text-primary px-8 py-3 rounded-lg font-semibold transition-colors"
                onClick={() => handleCTAClick('email', 'cta_section')}
              >
                Napísať email
              </a>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-playfair font-bold text-center text-gray-800 mb-12">
              Často kladené otázky
            </h2>
            <div className="max-w-3xl mx-auto space-y-6">
              {packageData.faq.map((item, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    {item.question}
                  </h3>
                  <p className="text-gray-600">
                    {item.answer}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Sticky CTA Button */}
        <a
          href="tel:+421951553464"
          className="sticky-cta bg-gold hover:bg-gold-dark text-white px-6 py-3 rounded-full font-semibold"
          onClick={() => handleCTAClick('phone', 'sticky')}
        >
          📞 Zavolať
        </a>

        {/* Footer */}
        <footer className="bg-gray-800 text-white py-8">
          <div className="container mx-auto px-6 text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <span className="font-playfair text-2xl font-semibold">eSpomienka</span>
            </div>
            <p className="text-gray-400">
              © 2025 eSpomienka. Všetky práva vyhradené.
            </p>
          </div>
        </footer>
      </div>
    </>
  )
}

export const getStaticPaths: GetStaticPaths = async () => {
  const paths = Object.keys(packagesData).map((slug) => ({
    params: { slug }
  }))

  return {
    paths,
    fallback: false
  }
}

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const slug = params?.slug as string
  const packageData = packagesData[slug]

  if (!packageData) {
    return {
      notFound: true
    }
  }

  return {
    props: {
      packageData
    }
  }
}
